CREATE TABLE invbg_client_mapping
(
    id              UUID                     DEFAULT gen_random_uuid() PRIMARY KEY,
    client_id       UUID    NOT NULL REFERENCES clients (id) ON DELETE CASCADE,
    invbg_client_id INTEGER NOT NULL,
    created_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE (client_id),
    UNIQUE (invbg_client_id)
);


CREATE INDEX idx_invbg_client_mapping_client_id ON invbg_client_mapping (client_id);
CREATE INDEX idx_invbg_client_mapping_invbg_client_id ON invbg_client_mapping (invbg_client_id);
