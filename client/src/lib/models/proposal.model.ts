import { Client } from './client.model';
import { VatInfo } from '../validators';

export interface Proposal {
  id: string;
  proposal_id: string;
  project_title: string;
  client: Client;
  client_name?: string; // Flattened for easy access
  client_email?: string; // Flattened for easy access
  client_rep?: string; // Flattened for easy access
  hourly_rate: number;
  expiration_date: string;
  created_at: string;
  price: number;
  currency: string;
  status: 'draft' | 'sent' | 'accepted' | 'partially_paid' | 'paid';
  agreed: boolean;
  accepted_at?: string;
  accepted_ip?: string;
  problems: Problem[];
  solutions: Solution[];
  scope: string[];
  timeline: TimelineItem[];
  pricing: PricingItem[];
  summary: string[];
  paid_amount?: number;
  initial_payment?: number; // The specific amount of the initial payment
  initial_invoice_id?: string;
  final_invoice_id?: string;
  vat?: VatInfo; // VAT information from Stripe Tax calculation
}

export interface Problem {
  title: string;
  description: string;
}

export interface Solution {
  title: string;
  description: string;
}

export interface TimelineItem {
  title: string;
  description: string;
  duration: string;
}

export interface PricingItem {
  item: string;
  hours: number;
  rate: number;
  total: number;
}
