import {
  InvBgService,
  InvBgV3Client,
  InvBgV3InvoiceItem,
  InvBgV3InvoiceRequest,
  InvBgV3VatInfo,
} from './invbg.service';
import { getSupabaseClient } from './supabase.service';
import { Proposal } from '../../lib/models';
import { getCountryMapping } from '../../lib/utils/country-mapping';
import { VatCalculatorService } from './vat-calculator.service';

export class InvoiceGeneratorService {
  private invBgService: InvBgService;

  constructor() {
    this.invBgService = new InvBgService();
  }

  /**
   * Pure function to determine if an invoice should be created
   */
  shouldCreateInvoice(proposal: Proposal): {
    shouldCreate: boolean;
    invoiceType: 'initial' | 'final' | null;
    reason?: string;
  } {
    // Check if client has billing information
    if (!proposal.client.billing_information) {
      return {
        shouldCreate: false,
        invoiceType: null,
        reason: 'No billing information available',
      };
    }

    // Determine invoice type based on status
    if (proposal.status === 'partially_paid' && !proposal.initial_invoice_id) {
      return {
        shouldCreate: true,
        invoiceType: 'initial',
      };
    }

    if (proposal.status === 'paid' && !proposal.final_invoice_id) {
      return {
        shouldCreate: true,
        invoiceType: 'final',
      };
    }

    return {
      shouldCreate: false,
      invoiceType: null,
      reason: 'Invoice already exists or status does not require invoice',
    };
  }

  /**
   * Pure function to build inv.bg v3 invoice data from proposal
   */
  /**
   * Get or create inv.bg client ID for our client
   */
  async getOrCreateInvBgClient(proposal: Proposal): Promise<number> {
    const billing = proposal.client.billing_information!;

    // Check if we already have a mapping
    const { data: existingMapping } = await getSupabaseClient()
      .from('invbg_client_mapping')
      .select('invbg_client_id')
      .eq('client_id', proposal.client.id)
      .single();

    if (existingMapping) {
      return existingMapping.invbg_client_id;
    }

    // Create new client in inv.bg
    const invBgClientData: InvBgV3Client = {
      name: billing.company_name!,
      name_en: billing.company_name!,
      address: billing.address!,
      address_en: billing.address!,
      town: billing.city!,
      town_en: billing.city!,
      country: getCountryMapping(billing.country || 'Bulgaria').bg,
      country_en: getCountryMapping(billing.country || 'Bulgaria').en,
      bulstat: billing.eik || null,
      mol: billing.mol || null,
      vat_number: billing.vat_number || undefined,
      is_reg_vat: !!billing.vat_number,
      is_person: false,
      email: proposal.client.email,
      phone: undefined,
    };

    const invBgClient = await this.invBgService.createClient(invBgClientData);

    // Store the mapping
    await getSupabaseClient().from('invbg_client_mapping').insert({
      client_id: proposal.client.id,
      invbg_client_id: invBgClient.id,
    });

    return invBgClient.id;
  }

  buildInvoiceData(
    proposal: Proposal,
    invoiceType: 'initial' | 'final',
    invBgClientId: number,
  ): InvBgV3InvoiceRequest {
    // Calculate amount based on invoice type
    const amount =
      invoiceType === 'initial'
        ? proposal.initial_payment || 0
        : proposal.price - (proposal.initial_payment || 0);

    // Get VAT information from stored VAT data or default to 20%
    const vatInfo: InvBgV3VatInfo = proposal.vat
      ? {
          percent: proposal.vat.percent,
          reason_without: proposal.vat.reason_without || undefined,
        }
      : {
          percent: 20, // Fallback to standard Bulgarian VAT rate
        };

    // Build invoice items for v3 API
    const items: InvBgV3InvoiceItem[] = [
      {
        name: `Project ${proposal.proposal_id} - ${invoiceType === 'initial' ? 'Initial Payment' : 'Final Payment'}`,
        quantity: 1,
        price: amount,
        quantity_unit: 'бр.',
        vat_percent: vatInfo.percent,
        discount_percent: 0,
      },
    ];

    // Calculate due date (30 days from now)
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 30);

    return {
      client: invBgClientId,
      items,
      currency: proposal.currency,
      date: new Date().toISOString().split('T')[0],
      due_date: dueDate.toISOString().split('T')[0],
      language: 'bg',
      payment_method: 'stripe',
      vat: vatInfo,
    };
  }

  /**
   * Main function to generate invoice for a proposal
   */
  async generateInvoiceForProposal(proposalId: string): Promise<{
    success: boolean;
    invoiceId?: string;
    invoiceType?: 'initial' | 'final';
    message: string;
  }> {
    try {
      // Fetch proposal with client and billing information
      const { data: proposal, error } = await getSupabaseClient()
        .from('proposals')
        .select(
          `
          *,
          client:clients (
            id,
            name,
            email,
            rep_full_name,
            billing_information (*)
          )
        `,
        )
        .eq('id', proposalId)
        .single();

      if (error || !proposal) {
        return {
          success: false,
          message: 'Proposal not found',
        };
      }

      // Transform billing information (it comes as array from Supabase)
      const transformedProposal: Proposal = {
        ...proposal,
        client: {
          ...proposal.client,
          billing_information: proposal.client.billing_information?.[0] || null,
        },
      };

      // Check if invoice should be created
      const shouldCreate = this.shouldCreateInvoice(transformedProposal);

      if (!shouldCreate.shouldCreate) {
        return {
          success: false,
          message: shouldCreate.reason || 'Invoice creation not needed',
        };
      }

      // Get or create inv.bg client
      const invBgClientId =
        await this.getOrCreateInvBgClient(transformedProposal);

      // Build invoice data
      const invoiceData = this.buildInvoiceData(
        transformedProposal,
        shouldCreate.invoiceType!,
        invBgClientId,
      );

      // Create invoice via inv.bg API
      const invoiceResponse =
        await this.invBgService.createInvoice(invoiceData);

      // Update proposal with invoice ID (convert number to string for storage)
      const updateField =
        shouldCreate.invoiceType === 'initial'
          ? 'initial_invoice_id'
          : 'final_invoice_id';

      const { error: updateError } = await getSupabaseClient()
        .from('proposals')
        .update({ [updateField]: invoiceResponse.id.toString() })
        .eq('id', proposalId);

      if (updateError) {
        console.error(
          'Failed to update proposal with invoice ID:',
          updateError,
        );
        // Invoice was created, but we couldn't save the ID - log this for manual handling
      }

      return {
        success: true,
        invoiceId: invoiceResponse.id.toString(),
        invoiceType: shouldCreate.invoiceType!,
        message: `${shouldCreate.invoiceType === 'initial' ? 'Initial' : 'Final'} invoice created successfully`,
      };
    } catch (error: any) {
      console.error('Invoice generation error:', error);
      return {
        success: false,
        message: `Failed to generate invoice: ${error.message}`,
      };
    }
  }

  /**
   * Download invoice PDF from inv.bg v3 API
   */
  async downloadInvoicePdf(
    invoiceId: number,
    language: 'bg' | 'en' = 'en',
  ): Promise<Buffer> {
    try {
      return await this.invBgService.downloadInvoicePdf(invoiceId, language);
    } catch (error: any) {
      console.error('PDF download error:', error);
      throw new Error(`Failed to download invoice PDF: ${error.message}`);
    }
  }
}
