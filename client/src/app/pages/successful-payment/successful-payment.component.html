<div class="min-h-screen bg-gradient-to-br from-[#f5f0ff] via-white to-[#ffe6f6] flex flex-col items-center justify-center px-6">

  <!-- Loading State -->
  <div *ngIf="loading" class="text-center">
    <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-[#7125bb] mx-auto mb-4"></div>
    <p class="text-lg text-gray-600">Loading...</p>
  </div>

  <!-- Success Message (when not showing billing form) -->
  <div *ngIf="!loading && !showBillingForm" class="text-center max-w-2xl">
    <svg class="w-24 h-24 text-green-500 mb-6 mx-auto" fill="none" stroke="currentColor" stroke-width="2"
         viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M5 13l4 4L19 7" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    <h1 class="text-4xl font-bold text-[#7125bb] mb-4">Payment Successful!</h1>
    <p class="text-lg text-gray-700 max-w-xl mx-auto mb-8">
      Thank you for your payment. Your proposal is now fully active and our team has been notified.
      We'll begin work shortly and keep you updated!
    </p>

    <div *ngIf="proposal?.client?.billing_information" class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-green-700 font-medium">Billing information complete - Your invoice will be generated shortly!</span>
      </div>
    </div>

    @if (proposalId) {
      <a [routerLink]="['/p', proposalId]"
         class="inline-block px-8 py-4 bg-gradient-to-r from-[#d734b1] to-[#7125bb] text-white font-semibold rounded-lg shadow-lg hover:from-[#7125bb] hover:to-[#d734b1] transition-all transform hover:scale-105">
        Back to Proposal
      </a>
    }
  </div>

  <!-- Billing Form -->
  <div *ngIf="!loading && showBillingForm" class="w-full max-w-4xl">
    <div class="text-center mb-8">
      <svg class="w-16 h-16 text-green-500 mb-4 mx-auto" fill="none" stroke="currentColor" stroke-width="2"
           viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path d="M5 13l4 4L19 7" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <h1 class="text-3xl font-bold text-[#7125bb] mb-2">Payment Successful!</h1>
      <p class="text-gray-600 mb-6">One more step to complete your order...</p>
    </div>

    <chm-billing-form
      [loading]="billingFormLoading"
      (formSubmit)="onBillingFormSubmit($event)">
    </chm-billing-form>
  </div>

  <!-- Toast Messages -->
  <p-toast></p-toast>
</div>
