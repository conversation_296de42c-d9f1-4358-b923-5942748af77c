/* Custom Table Styling */
::ng-deep .custom-table .p-datatable {
  background: transparent;
  border: none;
}

::ng-deep .custom-table .p-datatable-header {
  background: transparent;
  border: none;
  padding: 0;
}

::ng-deep .custom-table .p-datatable-thead > tr > th {
  background: rgba(51, 65, 85, 0.3);
  border: none;
  border-bottom: 1px solid rgba(71, 85, 105, 0.5);
  color: #e2e8f0;
  font-weight: 600;
  padding: 1rem;
}

::ng-deep .custom-table .p-datatable-tbody > tr {
  background: transparent;
  border: none;
  transition: background-color 0.2s ease;
}

::ng-deep .custom-table .p-datatable-tbody > tr:hover {
  background: rgba(51, 65, 85, 0.3) !important;
}

::ng-deep .custom-table .p-datatable-tbody > tr > td {
  border: none;
  border-bottom: 1px solid rgba(71, 85, 105, 0.2);
  padding: 1rem;
  color: #cbd5e1;
}

::ng-deep .custom-table .p-datatable-tbody > tr:last-child > td {
  border-bottom: none;
}

/* Checkbox Styling */
::ng-deep .custom-table .p-checkbox .p-checkbox-box {
  background: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 0.375rem;
}

::ng-deep .custom-table .p-checkbox .p-checkbox-box.p-highlight {
  background: #8b5cf6;
  border-color: #8b5cf6;
}

::ng-deep .custom-table .p-checkbox .p-checkbox-box:hover {
  border-color: #8b5cf6;
}

/* Paginator Styling */
::ng-deep .custom-table .p-paginator {
  background: rgba(51, 65, 85, 0.3);
  border: none;
  border-top: 1px solid rgba(71, 85, 105, 0.5);
  color: #e2e8f0;
  padding: 1rem;
}

::ng-deep .custom-table .p-paginator .p-paginator-pages .p-paginator-page {
  background: transparent;
  border: 1px solid rgba(71, 85, 105, 0.5);
  color: #e2e8f0;
  margin: 0 0.125rem;
  border-radius: 0.375rem;
}

::ng-deep .custom-table .p-paginator .p-paginator-pages .p-paginator-page:hover {
  background: rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
}

::ng-deep .custom-table .p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
  background: #8b5cf6;
  border-color: #8b5cf6;
  color: white;
}

::ng-deep .custom-table .p-paginator .p-select {
  background: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(71, 85, 105, 0.5);
  color: #e2e8f0;
}

/* Custom Select Styling */
::ng-deep .custom-dropdown .p-select {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(71, 85, 105, 1);
  border-radius: 0.5rem;
  color: white;
}

::ng-deep .custom-dropdown .p-select:hover {
  border-color: #8b5cf6;
}

::ng-deep .custom-dropdown .p-select:focus {
  border-color: #8b5cf6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

::ng-deep .custom-dropdown .p-select-overlay {
  background: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 0.5rem;
  backdrop-filter: blur(8px);
}

::ng-deep .custom-dropdown .p-select-option {
  color: #e2e8f0;
  padding: 0.75rem 1rem;
}

::ng-deep .custom-dropdown .p-select-option:hover {
  background: rgba(139, 92, 246, 0.2);
  color: white;
}

::ng-deep .custom-dropdown .p-select-option.p-selected {
  background: #8b5cf6;
  color: white;
}

/* Sort Icon Styling */
::ng-deep .custom-table .p-sortable-column .p-sortable-column-icon {
  color: #94a3b8;
}

::ng-deep .custom-table .p-sortable-column:hover .p-sortable-column-icon {
  color: #8b5cf6;
}

/* Loading Spinner */
::ng-deep .custom-table .p-datatable-loading-overlay {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
  ::ng-deep .custom-table .p-datatable-tbody > tr > td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }

  ::ng-deep .custom-table .p-datatable-thead > tr > th {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
}